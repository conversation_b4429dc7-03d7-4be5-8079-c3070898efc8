import numpy as np
import matplotlib.pyplot as plt
import torch
import torch.nn as nn

plt.rcParams['font.family'] = 'SimHei'
plt.rcParams['axes.unicode_minus'] = False

# 模拟训练数据
g_losses = [1.2, 1.1, 0.9, 0.8, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2] * 20
d_losses = [0.8, 0.7, 0.6, 0.5, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9] * 20
d_real_acc = [0.6, 0.65, 0.7, 0.75, 0.8, 0.75, 0.7, 0.65, 0.6, 0.55] * 20
d_fake_acc = [0.4, 0.45, 0.5, 0.55, 0.6, 0.55, 0.5, 0.45, 0.4, 0.35] * 20
js_divergence = [0.5, 0.4, 0.3, 0.2, 0.1, 0.15, 0.2, 0.25, 0.3, 0.35] * 20

fig = plt.figure(figsize=(20, 16))

# 子图1：损失函数曲线
ax1 = plt.subplot(3, 4, 1)
plt.plot(g_losses, label='生成器损失', color='red')
plt.plot(d_losses, label='判别器损失', color='blue')
plt.xlabel('训练轮次')
plt.ylabel('损失值')
plt.title('GAN训练损失曲线')
plt.legend()

# 子图2：3D损失景观（修正错误1：确保维度匹配）
ax2 = plt.subplot(3, 4, 5, projection='3d')
epochs_mesh = np.arange(0, len(g_losses), 5)
g_loss_mesh = np.array(g_losses)[::5]
d_loss_mesh = np.array(d_losses)[::5]
X, Y = np.meshgrid(epochs_mesh[:len(g_loss_mesh)], epochs_mesh[:len(d_loss_mesh)])
Z = np.outer(g_loss_mesh, d_loss_mesh)[:len(epochs_mesh), :len(epochs_mesh)]
surf = ax2.plot_surface(X, Y, Z, cmap='viridis')
ax2.set_title('GAN损失景观3D视图')

# 子图3：梯度范数分析（修正错误2：使用semilogy）
ax3 = plt.subplot(3, 4, 7)
g_grad_norm = np.array(g_losses) * 1.1 + 0.1
d_grad_norm = np.array(d_losses) * 1.2 + 0.05
plt.semilogy(g_grad_norm, label='生成器梯度范数', color='red')
plt.semilogy(d_grad_norm, label='判别器梯度范数', color='blue')
plt.title('梯度范数变化')
plt.legend()

# 子图4：稳定性热力图（修正错误3：添加索引边界检查）
ax4 = plt.subplot(3, 4, (9, 10))
stability_matrix = np.zeros((10, 20))
for i in range(10):
    for j in range(20):
        epoch_idx = i * 20 + j
        if epoch_idx < len(g_losses):  # 添加边界检查
            loss_stability = 1.0 / (1.0 + abs(g_losses[epoch_idx] - d_losses[epoch_idx]))
            stability_matrix[i, j] = loss_stability

plt.imshow(stability_matrix, cmap='RdYlBu')
plt.title('GAN训练稳定性热力图')

plt.tight_layout()
plt.show()